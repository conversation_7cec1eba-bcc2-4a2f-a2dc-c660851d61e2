"""
CSV Graph Builder

This module provides specialized graph building functionality for CSV data,
implementing deterministic ingestion for clear relationships and LLM assistance
for ambiguous data patterns.
"""

import logging
import json
from typing import List, Dict, Any, Optional, Tuple
from collections import defaultdict

from ..constants.schemas import (
    CSVSchemaInference, CSVRowChunk, CSVEntityExtraction,
    EntityRelationship, Entity
)
from ..constants.csv_processing import get_csv_relationship_mappings
from ..storage.neo4j_client import Neo4jClient
from ..llm.client import LLMClient
from .prompt_generator import create_csv_focused_generator

logger = logging.getLogger(__name__)


class CSVGraphBuilder:
    """
    Specialized graph builder for CSV data.
    
    This builder implements a hybrid approach:
    1. Deterministic ingestion for clear, structured relationships
    2. LLM assistance for ambiguous or complex data patterns
    3. Schema-aware entity and relationship creation
    """
    
    def __init__(
        self, 
        neo4j_client: Neo4jClient,
        llm_client: Optional[LLMClient] = None,
        use_llm_for_ambiguous: bool = True
    ):
        """
        Initialize the CSV graph builder.
        
        Args:
            neo4j_client: Neo4j client for graph operations
            llm_client: Optional LLM client for ambiguous data processing
            use_llm_for_ambiguous: Whether to use LLM for unclear patterns
        """
        self.neo4j_client = neo4j_client
        self.llm_client = llm_client
        self.use_llm_for_ambiguous = use_llm_for_ambiguous
        self.relationship_mappings = get_csv_relationship_mappings()
        self.prompt_generator = create_csv_focused_generator()
        
        # Statistics tracking
        self.stats = {
            'deterministic_extractions': 0,
            'llm_assisted_extractions': 0,
            'entities_created': 0,
            'relationships_created': 0,
            'failed_extractions': 0
        }
    
    def build_csv_graph(
        self, 
        file_id: str,
        csv_chunks: List[CSVRowChunk],
        schema_inference: CSVSchemaInference
    ) -> Dict[str, Any]:
        """
        Build a knowledge graph from CSV chunks using the inferred schema.
        
        Args:
            file_id: Unique identifier for the CSV file
            csv_chunks: List of CSV row chunks
            schema_inference: Inferred schema information
            
        Returns:
            Dictionary with build results and statistics
        """
        logger.info(f"Building CSV graph for {file_id} with {len(csv_chunks)} chunks")
        
        # Reset statistics
        self.stats = {k: 0 for k in self.stats}
        
        # Create file node
        self._create_csv_file_node(file_id, schema_inference)
        
        # Process each chunk
        all_extractions = []
        for chunk in csv_chunks:
            extractions = self._process_csv_chunk(chunk, schema_inference)
            all_extractions.extend(extractions)
        
        # Build graph from extractions
        graph_result = self._build_graph_from_extractions(file_id, all_extractions, csv_chunks)
        
        # Add statistics
        graph_result['statistics'] = self.stats.copy()
        
        logger.info(f"CSV graph building completed. Stats: {self.stats}")
        return graph_result
    
    def _create_csv_file_node(self, file_id: str, schema_inference: CSVSchemaInference) -> bool:
        """Create a node representing the CSV file itself."""
        try:
            query = """
            MERGE (f:DataSource {id: $file_id})
            SET f.name = $file_name,
                f.type = 'CSV',
                f.total_rows = $total_rows,
                f.total_columns = $total_columns,
                f.primary_entity_type = $primary_entity,
                f.schema_confidence = $confidence,
                f.created_at = datetime()
            RETURN f
            """
            
            result = self.neo4j_client.execute_query(query, {
                'file_id': file_id,
                'file_name': schema_inference.file_name,
                'total_rows': schema_inference.total_rows,
                'total_columns': schema_inference.total_columns,
                'primary_entity': schema_inference.primary_entity_type,
                'confidence': schema_inference.schema_confidence
            })
            
            return len(result) > 0
            
        except Exception as e:
            logger.error(f"Failed to create CSV file node: {e}")
            return False
    
    def _process_csv_chunk(
        self, 
        chunk: CSVRowChunk, 
        schema_inference: CSVSchemaInference
    ) -> List[CSVEntityExtraction]:
        """
        Process a CSV chunk to extract entities and relationships.
        
        Args:
            chunk: CSV row chunk to process
            schema_inference: Schema information
            
        Returns:
            List of entity extractions
        """
        extractions = []
        
        for row_idx, row_data in enumerate(chunk.rows):
            try:
                # Try deterministic extraction first
                extraction = self._deterministic_extraction(
                    chunk.chunk_id, row_idx, row_data, chunk.headers, schema_inference
                )
                
                if extraction and self._is_extraction_complete(extraction, schema_inference):
                    extraction.extraction_method = "deterministic"
                    extraction.confidence_score = 0.9
                    self.stats['deterministic_extractions'] += 1
                else:
                    # Fall back to LLM assistance if available and enabled
                    if self.use_llm_for_ambiguous and self.llm_client:
                        extraction = self._llm_assisted_extraction(
                            chunk.chunk_id, row_idx, row_data, chunk.headers, schema_inference
                        )
                        if extraction:
                            extraction.extraction_method = "llm_assisted"
                            extraction.confidence_score = 0.7
                            self.stats['llm_assisted_extractions'] += 1
                    
                    if not extraction:
                        self.stats['failed_extractions'] += 1
                        continue
                
                extractions.append(extraction)
                
            except Exception as e:
                logger.error(f"Failed to process row {row_idx} in chunk {chunk.chunk_id}: {e}")
                self.stats['failed_extractions'] += 1
        
        return extractions
    
    def _deterministic_extraction(
        self,
        chunk_id: str,
        row_idx: int,
        row_data: Dict[str, str],
        headers: List[str],
        schema_inference: CSVSchemaInference
    ) -> Optional[CSVEntityExtraction]:
        """
        Perform deterministic entity and relationship extraction.

        This method uses the schema inference and predefined patterns
        to extract entities and relationships without LLM assistance.
        """
        entities = []
        relationships = []

        # Extract entities from all non-empty columns
        primary_entity = None
        entity_map = {}  # Map column values to entity names

        for col_name, col_value in row_data.items():
            if not col_value or col_value.strip() == '':
                continue

            col_value = col_value.strip()

            # Skip URLs and very long text
            if col_value.startswith('http') or len(col_value) > 200:
                continue

            # Determine entity type based on column name and value
            entity_type = self._determine_entity_type_for_column(col_name, col_value)

            if entity_type:
                # Create entity
                entity = Entity(
                    name=col_value,
                    entity_type=entity_type
                )
                entities.append(entity)
                entity_map[col_name] = col_value

                # Mark as primary entity if it's a person (assigned to)
                if col_name.lower() in ['assigned to', 'assigned_to'] and primary_entity is None:
                    primary_entity = col_value

        # If no primary entity found, use the first entity
        if not primary_entity and entities:
            primary_entity = entities[0].name

        # Create relationships between entities
        if primary_entity and len(entities) > 1:
            for col_name, col_value in entity_map.items():
                if col_value != primary_entity:
                    # Determine relationship type
                    relationship_type = self._get_relationship_type_for_columns(col_name, col_value)

                    if relationship_type:
                        # Get entity types
                        subject_type = self._determine_entity_type_for_column("assigned_to", primary_entity) or "Person"
                        object_type = self._determine_entity_type_for_column(col_name, col_value) or "Entity"

                        relationship = EntityRelationship(
                            subject=primary_entity,
                            predicate=relationship_type,
                            object=col_value,
                            subject_type=subject_type,
                            object_type=object_type
                        )
                        relationships.append(relationship)

        return CSVEntityExtraction(
            chunk_id=chunk_id,
            row_index=row_idx,
            entities=entities,
            relationships=relationships,
            row_data=row_data
        )

    def _determine_entity_type_for_column(self, col_name: str, col_value: str) -> Optional[str]:
        """
        Determine entity type based on column name and value.

        Args:
            col_name: Column name
            col_value: Column value

        Returns:
            Entity type or None
        """
        col_lower = col_name.lower()
        value_lower = col_value.lower()

        # Person names (assigned to column or person-like names)
        if 'assigned' in col_lower or 'author' in col_lower or 'writer' in col_lower:
            return "Person"

        # Industry/Domain
        if 'industry' in col_lower or col_value in ['Retail', 'Healthcare', 'Finance', 'Automobile', 'Education']:
            return "Industry"

        # Technology/Expertise
        if 'expertise' in col_lower or 'technology' in col_lower or col_value in ['AI', 'Blockchain', 'IoT']:
            return "Technology"

        # Status/State
        if 'status' in col_lower or col_value in ['Done', 'In progress', 'Completed']:
            return "Status"

        # Generic categories
        if col_value in ['Generic - Human Brain']:
            return "Category"

        # Default for non-empty meaningful values
        if len(col_value) > 1 and not col_value.isdigit():
            return "Entity"

        return None

    def _get_relationship_type_for_columns(self, col_name: str, col_value: str) -> Optional[str]:
        """
        Get relationship type based on column name and value.

        Args:
            col_name: Column name
            col_value: Column value

        Returns:
            Relationship type or None
        """
        col_lower = col_name.lower()

        # Industry relationships
        if 'industry' in col_lower:
            return "WORKS_IN"

        # Expertise/Technology relationships
        if 'expertise' in col_lower or 'technology' in col_lower:
            return "SPECIALIZES_IN"

        # Status relationships
        if 'status' in col_lower:
            return "HAS_STATUS"

        # Default relationship
        return "RELATED_TO"
    
    def _llm_assisted_extraction(
        self,
        chunk_id: str,
        row_idx: int,
        row_data: Dict[str, str],
        headers: List[str],
        schema_inference: CSVSchemaInference
    ) -> Optional[CSVEntityExtraction]:
        """
        Use LLM to extract entities and relationships from ambiguous data.
        """
        if not self.llm_client:
            return None
        
        try:
            # Format row data for LLM
            row_text = ", ".join([f"{k}: {v}" for k, v in row_data.items() if v])
            
            # Generate prompt
            prompt = self.prompt_generator.generate_csv_extraction_prompt(
                csv_content=f"Headers: {', '.join(headers)}\nRow: {row_text}",
                headers=headers,
                schema_inference={
                    'primary_entity_type': schema_inference.primary_entity_type,
                    'identifier_columns': schema_inference.identifier_columns,
                    'relationship_columns': schema_inference.relationship_columns
                }
            )
            
            # Get LLM response
            response = self.llm_client.generate_text(prompt)
            
            # Parse response
            extraction_data = self._parse_llm_response(response)
            
            if extraction_data:
                entities = [
                    Entity(name=e['name'], entity_type=e['type'])
                    for e in extraction_data.get('entities', [])
                ]
                
                relationships = [
                    EntityRelationship(
                        subject=r['subject'],
                        predicate=r['predicate'],
                        object=r['object'],
                        subject_type=r.get('subject_type', 'Entity'),
                        object_type=r.get('object_type', 'Entity')
                    )
                    for r in extraction_data.get('relationships', [])
                ]
                
                return CSVEntityExtraction(
                    chunk_id=chunk_id,
                    row_index=row_idx,
                    entities=entities,
                    relationships=relationships,
                    row_data=row_data
                )
        
        except Exception as e:
            logger.error(f"LLM-assisted extraction failed for row {row_idx}: {e}")
        
        return None
    
    def _parse_llm_response(self, response: str) -> Optional[Dict[str, Any]]:
        """Parse LLM response to extract structured data."""
        try:
            # Try to find JSON in the response
            start_idx = response.find('{')
            end_idx = response.rfind('}') + 1
            
            if start_idx >= 0 and end_idx > start_idx:
                json_str = response[start_idx:end_idx]
                return json.loads(json_str)
        
        except Exception as e:
            logger.error(f"Failed to parse LLM response: {e}")
        
        return None
    
    def _infer_target_entity_type(self, column_name: str) -> str:
        """Infer the target entity type from column name."""
        col_lower = column_name.lower()
        
        if 'manager' in col_lower or 'lead' in col_lower or 'owner' in col_lower:
            return "Person"
        elif 'project' in col_lower:
            return "Project"
        elif 'department' in col_lower or 'dept' in col_lower:
            return "Department"
        elif 'team' in col_lower:
            return "Team"
        elif 'company' in col_lower or 'client' in col_lower:
            return "Company"
        else:
            return "Entity"
    
    def _get_relationship_type(self, column_name: str) -> Optional[str]:
        """Get relationship type from column name using mappings."""
        col_lower = column_name.lower()
        
        for category, mappings in self.relationship_mappings.items():
            for pattern, relationship in mappings.items():
                if pattern in col_lower:
                    return relationship
        
        return "RELATED_TO"  # Default relationship
    
    def _is_extraction_complete(
        self,
        extraction: CSVEntityExtraction,
        schema_inference: CSVSchemaInference
    ) -> bool:
        """Check if deterministic extraction is complete enough."""
        # Consider extraction complete if we have at least one entity
        # and relationships for foreign key columns
        return len(extraction.entities) > 0

    def _build_graph_from_extractions(
        self,
        file_id: str,
        extractions: List[CSVEntityExtraction],
        chunks: List[CSVRowChunk]
    ) -> Dict[str, Any]:
        """
        Build the actual graph in Neo4j from the extractions.

        Args:
            file_id: CSV file identifier
            extractions: List of entity extractions
            chunks: Original CSV chunks

        Returns:
            Build result summary
        """
        entities_created = 0
        relationships_created = 0
        chunk_nodes_created = 0

        try:
            # Create chunk nodes
            for chunk in chunks:
                chunk_created = self._create_chunk_node(file_id, chunk)
                if chunk_created:
                    chunk_nodes_created += 1

            # Process extractions to create entities and relationships
            entity_cache = {}  # Cache to avoid duplicate entities

            for extraction in extractions:
                # Create entities
                for entity in extraction.entities:
                    entity_key = f"{entity.entity_type}:{entity.name}"

                    if entity_key not in entity_cache:
                        entity_created = self._create_entity_node(entity, extraction.chunk_id)
                        if entity_created:
                            entities_created += 1
                            entity_cache[entity_key] = True

                # Create relationships
                for relationship in extraction.relationships:
                    rel_created = self._create_relationship(relationship, extraction.chunk_id)
                    if rel_created:
                        relationships_created += 1

            # Update statistics
            self.stats['entities_created'] = entities_created
            self.stats['relationships_created'] = relationships_created

            return {
                'success': True,
                'file_id': file_id,
                'chunks_processed': len(chunks),
                'extractions_processed': len(extractions),
                'entities_created': entities_created,
                'relationships_created': relationships_created,
                'chunk_nodes_created': chunk_nodes_created
            }

        except Exception as e:
            logger.error(f"Failed to build graph from extractions: {e}")
            return {
                'success': False,
                'error': str(e),
                'file_id': file_id
            }

    def _create_chunk_node(self, file_id: str, chunk: CSVRowChunk) -> bool:
        """Create a chunk node in Neo4j."""
        try:
            query = """
            MATCH (f:DataSource {id: $file_id})
            MERGE (c:Chunk {id: $chunk_id})
            SET c.chunk_index = $chunk_index,
                c.start_row = $start_row,
                c.end_row = $end_row,
                c.row_count = $row_count,
                c.processing_strategy = $strategy,
                c.created_at = datetime()
            MERGE (f)-[:CONTAINS]->(c)
            RETURN c
            """

            result = self.neo4j_client.execute_query(query, {
                'file_id': file_id,
                'chunk_id': chunk.chunk_id,
                'chunk_index': chunk.chunk_index,
                'start_row': chunk.start_row,
                'end_row': chunk.end_row,
                'row_count': len(chunk.rows),
                'strategy': chunk.processing_strategy
            })

            return len(result) > 0

        except Exception as e:
            logger.error(f"Failed to create chunk node {chunk.chunk_id}: {e}")
            return False

    def _create_entity_node(self, entity: Entity, chunk_id: str) -> bool:
        """Create an entity node in Neo4j."""
        try:
            # Use dynamic label based on entity type
            query = f"""
            MERGE (e:{entity.entity_type} {{name: $name}})
            SET e.entity_type = $entity_type,
                e.created_at = coalesce(e.created_at, datetime()),
                e.updated_at = datetime()
            WITH e
            MATCH (c:Chunk {{id: $chunk_id}})
            MERGE (c)-[:EXTRACTED_FROM]->(e)
            RETURN e
            """

            result = self.neo4j_client.execute_query(query, {
                'name': entity.name,
                'entity_type': entity.entity_type,
                'chunk_id': chunk_id
            })

            return len(result) > 0

        except Exception as e:
            logger.error(f"Failed to create entity node {entity.name}: {e}")
            return False

    def _create_relationship(self, relationship: EntityRelationship, chunk_id: str) -> bool:
        """Create a relationship in Neo4j."""
        try:
            query = f"""
            MATCH (s:{relationship.subject_type} {{name: $subject}})
            MATCH (o:{relationship.object_type} {{name: $object}})
            MERGE (s)-[r:{relationship.predicate}]->(o)
            SET r.created_at = coalesce(r.created_at, datetime()),
                r.updated_at = datetime(),
                r.source_chunk = $chunk_id
            RETURN r
            """

            result = self.neo4j_client.execute_query(query, {
                'subject': relationship.subject,
                'object': relationship.object,
                'chunk_id': chunk_id
            })

            return len(result) > 0

        except Exception as e:
            logger.error(f"Failed to create relationship {relationship.subject} -> {relationship.predicate} -> {relationship.object}: {e}")
            return False

    def get_build_statistics(self) -> Dict[str, Any]:
        """Get current build statistics."""
        return self.stats.copy()
