#!/usr/bin/env python3
"""
Clear CSV Graph Data

Clear only the CSV-related graph data from Neo4j, keeping other data intact.
"""

import os
import sys

# Add the parent directory to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from enterprise_kg_minimal.storage.neo4j_client import Neo4jClient, Neo4jConnection


def load_config():
    """Load configuration from .env file."""
    try:
        from dotenv import load_dotenv
        load_dotenv()
    except ImportError:
        pass
    
    return {
        "neo4j_uri": os.getenv("NEO4J_URI"),
        "neo4j_user": os.getenv("NEO4J_USER"),
        "neo4j_password": os.getenv("NEO4J_PASSWORD"),
        "neo4j_database": os.getenv("NEO4J_DATABASE"),
    }


def clear_csv_data():
    """Clear only CSV-related data from Neo4j."""
    config = load_config()
    
    neo4j_conn = Neo4jConnection(
        uri=config["neo4j_uri"],
        user=config["neo4j_user"],
        password=config["neo4j_password"],
        database=config["neo4j_database"]
    )
    neo4j_client = Neo4jClient(neo4j_conn)
    
    print("🧹 Clearing CSV-related graph data...")
    
    # First, let's see what CSV data exists
    print("\n📊 Current CSV-related data:")
    
    # Check DataSource nodes related to CSV
    ds_query = """
    MATCH (ds:DataSource) 
    WHERE ds.type = 'CSV' OR ds.id CONTAINS 'csv' OR ds.id CONTAINS 'blogs_tracker'
    RETURN ds.id as id, ds.name as name, ds.type as type
    """
    ds_results = neo4j_client.execute_query(ds_query)
    
    csv_datasource_ids = []
    if ds_results:
        print("   DataSource nodes:")
        for ds in ds_results:
            print(f"     - {ds['id']} ({ds['name']}, {ds['type']})")
            csv_datasource_ids.append(ds['id'])
    
    # Check chunks related to CSV
    chunk_query = """
    MATCH (c:Chunk) 
    WHERE c.id CONTAINS 'csv' OR c.id CONTAINS 'blogs_tracker'
    RETURN count(c) as chunk_count
    """
    chunk_results = neo4j_client.execute_query(chunk_query)
    chunk_count = chunk_results[0]['chunk_count'] if chunk_results else 0
    print(f"   Chunk nodes: {chunk_count}")
    
    # Now clear the CSV data step by step
    print("\n🗑️ Clearing CSV data...")
    
    # Step 1: Clear entities extracted from CSV chunks
    clear_entities_query = """
    MATCH (c:Chunk)-[:EXTRACTED_FROM]->(e)
    WHERE c.id CONTAINS 'csv' OR c.id CONTAINS 'blogs_tracker'
    WITH e
    DETACH DELETE e
    """
    result1 = neo4j_client.execute_query(clear_entities_query)
    print("   ✅ Cleared entities extracted from CSV chunks")
    
    # Step 2: Clear CSV chunks
    clear_chunks_query = """
    MATCH (c:Chunk)
    WHERE c.id CONTAINS 'csv' OR c.id CONTAINS 'blogs_tracker'
    DETACH DELETE c
    """
    result2 = neo4j_client.execute_query(clear_chunks_query)
    print("   ✅ Cleared CSV chunks")
    
    # Step 3: Clear CSV DataSource nodes
    clear_datasource_query = """
    MATCH (ds:DataSource)
    WHERE ds.type = 'CSV' OR ds.id CONTAINS 'csv' OR ds.id CONTAINS 'blogs_tracker'
    DETACH DELETE ds
    """
    result3 = neo4j_client.execute_query(clear_datasource_query)
    print("   ✅ Cleared CSV DataSource nodes")
    
    # Step 4: Clean up any orphaned nodes that might have been created
    orphan_cleanup_query = """
    MATCH (n)
    WHERE NOT (n)--()
    AND (n.name IN ['Amritha', 'Manish'] OR 
         n.name CONTAINS 'AI' OR 
         n.name CONTAINS 'Retail' OR 
         n.name CONTAINS 'Blockchain')
    DELETE n
    """
    result4 = neo4j_client.execute_query(orphan_cleanup_query)
    print("   ✅ Cleaned up orphaned CSV-related nodes")
    
    # Verify cleanup
    print("\n🔍 Verifying cleanup...")
    
    # Check remaining DataSource nodes
    remaining_ds_query = "MATCH (ds:DataSource) RETURN count(ds) as count"
    remaining_ds = neo4j_client.execute_query(remaining_ds_query)
    ds_count = remaining_ds[0]['count'] if remaining_ds else 0
    print(f"   Remaining DataSource nodes: {ds_count}")
    
    # Check remaining chunks
    remaining_chunk_query = "MATCH (c:Chunk) RETURN count(c) as count"
    remaining_chunks = neo4j_client.execute_query(remaining_chunk_query)
    chunk_count = remaining_chunks[0]['count'] if remaining_chunks else 0
    print(f"   Remaining Chunk nodes: {chunk_count}")
    
    # Check total nodes
    total_nodes_query = "MATCH (n) RETURN count(n) as count"
    total_nodes = neo4j_client.execute_query(total_nodes_query)
    total_count = total_nodes[0]['count'] if total_nodes else 0
    print(f"   Total remaining nodes: {total_count}")
    
    neo4j_client.close()
    
    print("\n✅ CSV graph data cleared successfully!")
    print("   Other graph data remains intact.")


if __name__ == "__main__":
    clear_csv_data()
