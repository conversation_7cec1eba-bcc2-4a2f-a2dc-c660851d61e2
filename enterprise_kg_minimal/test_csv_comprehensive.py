#!/usr/bin/env python3
"""
Comprehensive CSV Processing Test

Test the CSV processing with both deterministic and LLM extraction
using the real CSV file from the documents folder.
"""

import os
import sys
import logging
from typing import Dict, Any

# Add the parent directory to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from enterprise_kg_minimal.core.document_processor import process_document
from enterprise_kg_minimal.storage.neo4j_client import Neo4j<PERSON>lient, Neo4jConnection

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def load_config() -> Dict[str, Any]:
    """Load configuration from .env file."""
    try:
        from dotenv import load_dotenv
        load_dotenv()
    except ImportError:
        logger.warning("python-dotenv not installed, using environment variables directly")
    
    config = {
        "neo4j_uri": os.getenv("NEO4J_URI"),
        "neo4j_user": os.getenv("NEO4J_USER"),
        "neo4j_password": os.getenv("NEO4J_PASSWORD"),
        "neo4j_database": os.getenv("NEO4J_DATABASE"),
        "llm_provider": os.getenv("LLM_PROVIDER", "requesty"),
        "llm_model": os.getenv("LLM_MODEL", "anthropic/claude-3-5-sonnet-20241022"),
        "llm_api_key": os.getenv("REQUESTY_API_KEY")
    }
    
    # Validate required configuration
    missing_configs = []
    for key, value in config.items():
        if not value and key != "neo4j_database":  # neo4j_database is optional
            missing_configs.append(key)
    
    if missing_configs:
        raise ValueError(f"Missing required configuration: {', '.join(missing_configs)}")
    
    return config


def load_csv_file() -> str:
    """Load the CSV file from documents folder."""
    csv_path = "documents/_Blogs Tracker _ Leadgen - Linkedin.csv"
    
    if not os.path.exists(csv_path):
        raise FileNotFoundError(f"CSV file not found: {csv_path}")
    
    with open(csv_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    logger.info(f"Loaded CSV file: {len(content)} characters, {len(content.splitlines())} lines")
    return content


def clear_existing_data(config: Dict[str, Any]) -> bool:
    """Clear existing test data from Neo4j."""
    try:
        logger.info("🧹 Clearing existing CSV test data from Neo4j...")
        
        neo4j_conn = Neo4jConnection(
            uri=config["neo4j_uri"],
            user=config["neo4j_user"],
            password=config["neo4j_password"],
            database=config["neo4j_database"]
        )
        neo4j_client = Neo4jClient(neo4j_conn)
        
        # Delete test data related to our CSV file
        queries = [
            "MATCH (n) WHERE n.id = 'blogs_tracker_comprehensive.csv' DETACH DELETE n",
            "MATCH (n:DataSource) WHERE n.name CONTAINS 'Blogs Tracker Comprehensive' DETACH DELETE n",
            "MATCH (c:Chunk) WHERE c.id CONTAINS 'blogs_tracker_comprehensive' DETACH DELETE c"
        ]
        
        for query in queries:
            result = neo4j_client.execute_query(query)
            logger.debug(f"Executed cleanup query: {query}")
        
        neo4j_client.close()
        logger.info("✅ Existing test data cleared")
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to clear existing data: {e}")
        return False


def process_csv_comprehensive(csv_content: str, config: Dict[str, Any]) -> Dict[str, Any]:
    """Process the CSV file using comprehensive extraction (deterministic + LLM)."""
    logger.info("\n🚀 Processing CSV file with comprehensive extraction (deterministic + LLM)...")
    
    try:
        result = process_document(
            file_id="blogs_tracker_comprehensive.csv",
            file_content=csv_content,
            neo4j_uri=config["neo4j_uri"],
            neo4j_user=config["neo4j_user"],
            neo4j_password=config["neo4j_password"],
            neo4j_database=config["neo4j_database"],
            llm_provider=config["llm_provider"],
            llm_model=config["llm_model"],
            llm_api_key=config["llm_api_key"],
            content_type="csv"  # Force CSV processing
        )
        
        if result["success"]:
            logger.info("✅ CSV processing completed successfully!")
            logger.info(f"📊 Processing Results:")
            logger.info(f"   Content Type: {result['content_type']}")
            logger.info(f"   Chunks Created: {result['chunks_created']}")
            logger.info(f"   Total Entities: {result['total_entities']}")
            logger.info(f"   Total Relationships: {result['total_relationships']}")
            
            if 'schema_inference' in result:
                schema = result['schema_inference']
                logger.info(f"🔍 Schema Analysis:")
                logger.info(f"   Primary Entity: {schema['primary_entity_type']}")
                logger.info(f"   Confidence: {schema['confidence']:.2f}")
                logger.info(f"   Identifier Columns: {schema['identifier_columns']}")
                logger.info(f"   Relationship Columns: {schema['relationship_columns']}")
                
                if schema['suggested_relationships']:
                    logger.info(f"🔗 Suggested Relationships:")
                    for subj, pred, obj in schema['suggested_relationships']:
                        logger.info(f"   {subj} -> {pred} -> {obj}")
            
            if 'processing_statistics' in result:
                stats = result['processing_statistics']
                logger.info(f"📈 Processing Statistics:")
                for key, value in stats.items():
                    logger.info(f"   {key.replace('_', ' ').title()}: {value}")
        else:
            logger.error(f"❌ CSV processing failed: {result.get('error', 'Unknown error')}")
        
        return result
        
    except Exception as e:
        logger.error(f"❌ CSV processing failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return {"success": False, "error": str(e)}


def validate_comprehensive_graph(config: Dict[str, Any]) -> Dict[str, Any]:
    """Validate the created graph structure with detailed analysis."""
    logger.info("\n🔍 Validating Comprehensive Graph Structure...")
    
    try:
        neo4j_conn = Neo4jConnection(
            uri=config["neo4j_uri"],
            user=config["neo4j_user"],
            password=config["neo4j_password"],
            database=config["neo4j_database"]
        )
        neo4j_client = Neo4jClient(neo4j_conn)
        
        validation_results = {}
        
        # 1. Check DataSource node
        datasource_query = "MATCH (ds:DataSource) WHERE ds.id = 'blogs_tracker_comprehensive.csv' RETURN ds"
        datasource_result = neo4j_client.execute_query(datasource_query)
        validation_results['datasource_created'] = len(datasource_result) > 0
        
        if validation_results['datasource_created']:
            ds = datasource_result[0]['ds']
            logger.info(f"✅ DataSource node found:")
            logger.info(f"   Name: {ds.get('name', 'N/A')}")
            logger.info(f"   Type: {ds.get('type', 'N/A')}")
            logger.info(f"   Rows: {ds.get('total_rows', 'N/A')}")
            logger.info(f"   Columns: {ds.get('total_columns', 'N/A')}")
        
        # 2. Check Chunk nodes
        chunk_query = "MATCH (c:Chunk) WHERE c.id CONTAINS 'blogs_tracker_comprehensive' RETURN count(c) as chunk_count"
        chunk_result = neo4j_client.execute_query(chunk_query)
        chunk_count = chunk_result[0]['chunk_count'] if chunk_result else 0
        validation_results['chunk_count'] = chunk_count
        logger.info(f"📦 Chunks created: {chunk_count}")
        
        # 3. Check specific entities we expect
        expected_entities = {
            "Person": ["Amritha", "Manish"],
            "Industry": ["Retail", "Healthcare", "Finance", "Automobile"],
            "Technology": ["AI", "Blockchain", "IoT"],
            "Status": ["Done", "In progress"]
        }
        
        entity_counts = {}
        found_entities = {}
        
        for entity_type, expected_names in expected_entities.items():
            query = f"MATCH (n:{entity_type}) RETURN count(n) as count, collect(n.name) as names"
            result = neo4j_client.execute_query(query)
            
            if result:
                count = result[0]["count"]
                names = result[0]["names"]
                entity_counts[entity_type] = count
                found_entities[entity_type] = names
                
                logger.info(f"👥 {entity_type} entities: {count}")
                if names:
                    logger.info(f"   Names: {', '.join(names[:10])}")  # Show first 10
                
                # Check if expected entities are found
                found_expected = [name for name in expected_names if name in names]
                if found_expected:
                    logger.info(f"   ✅ Found expected: {', '.join(found_expected)}")
                else:
                    logger.warning(f"   ⚠️ No expected entities found for {entity_type}")
        
        validation_results['entity_counts'] = entity_counts
        validation_results['found_entities'] = found_entities
        
        # 4. Check relationships
        rel_query = "MATCH ()-[r]->() RETURN type(r) as rel_type, count(r) as count ORDER BY count DESC"
        rel_results = neo4j_client.execute_query(rel_query)
        relationship_counts = {r["rel_type"]: r["count"] for r in rel_results}
        validation_results['relationship_counts'] = relationship_counts
        
        logger.info(f"🔗 Relationship Counts:")
        for rel_type, count in relationship_counts.items():
            logger.info(f"   {rel_type}: {count}")
        
        # 5. Check specific relationships we expect
        expected_relationships = [
            ("Person", "WORKS_IN", "Industry"),
            ("Person", "SPECIALIZES_IN", "Technology"),
            ("Person", "HAS_STATUS", "Status")
        ]
        
        found_relationships = []
        for subj_type, rel_type, obj_type in expected_relationships:
            rel_check_query = f"""
            MATCH (s:{subj_type})-[r:{rel_type}]->(o:{obj_type})
            RETURN s.name as subject, o.name as object
            LIMIT 5
            """
            rel_check_result = neo4j_client.execute_query(rel_check_query)
            
            if rel_check_result:
                found_relationships.append((subj_type, rel_type, obj_type))
                logger.info(f"✅ Found {rel_type} relationships:")
                for rel in rel_check_result:
                    logger.info(f"   {rel['subject']} -> {rel_type} -> {rel['object']}")
        
        validation_results['found_relationships'] = found_relationships
        
        # 6. Overall validation
        total_entities = sum(entity_counts.values())
        total_relationships = sum(relationship_counts.values())
        
        validation_results['total_entities'] = total_entities
        validation_results['total_relationships'] = total_relationships
        
        # Success criteria:
        # - DataSource created
        # - Chunks created
        # - At least some entities created
        # - At least some relationships created
        # - Found expected people (Amritha, Manish)
        person_names = found_entities.get("Person", [])
        expected_people_found = ("Amritha" in person_names or "Manish" in person_names)
        
        validation_results['success'] = (
            validation_results['datasource_created'] and
            chunk_count > 0 and
            total_entities > 0 and
            total_relationships > 0 and
            expected_people_found
        )
        
        neo4j_client.close()
        
        logger.info(f"\n📊 Comprehensive Validation Summary:")
        logger.info(f"   DataSource Created: {'✅' if validation_results['datasource_created'] else '❌'}")
        logger.info(f"   Chunks Created: {chunk_count}")
        logger.info(f"   Total Entities: {total_entities}")
        logger.info(f"   Total Relationships: {total_relationships}")
        logger.info(f"   Expected People Found: {'✅' if expected_people_found else '❌'}")
        logger.info(f"   Overall Success: {'✅' if validation_results['success'] else '❌'}")
        
        return validation_results
        
    except Exception as e:
        logger.error(f"❌ Graph validation failed: {e}")
        import traceback
        traceback.print_exc()
        return {"success": False, "error": str(e)}


def main():
    """Main test function."""
    logger.info("🚀 Comprehensive CSV Processing Test")
    logger.info("=" * 60)
    
    try:
        # Load configuration
        config = load_config()
        logger.info("✅ Configuration loaded from .env")
        
        # Load CSV file
        csv_content = load_csv_file()
        logger.info("✅ CSV file loaded")
        
        # Clear existing data
        if not clear_existing_data(config):
            logger.warning("⚠️ Failed to clear existing data, continuing anyway...")
        
        # Process CSV file with comprehensive extraction
        processing_result = process_csv_comprehensive(csv_content, config)
        
        if not processing_result["success"]:
            logger.error("❌ CSV processing failed, skipping validation")
            return False
        
        # Validate graph structure
        validation_result = validate_comprehensive_graph(config)
        
        # Final summary
        logger.info("\n🎯 Comprehensive Test Results Summary")
        logger.info("=" * 60)
        
        if processing_result["success"] and validation_result["success"]:
            logger.info("🎉 Comprehensive CSV processing test passed!")
            logger.info(f"\n📈 Key Metrics:")
            logger.info(f"   CSV Rows Processed: ~278 rows")
            logger.info(f"   Entities Created: {validation_result['total_entities']}")
            logger.info(f"   Relationships Created: {validation_result['total_relationships']}")
            logger.info(f"   Chunks Created: {validation_result['chunk_count']}")
            
            # Show entity breakdown
            logger.info(f"\n👥 Entity Breakdown:")
            for entity_type, count in validation_result['entity_counts'].items():
                logger.info(f"   {entity_type}: {count}")
            
            logger.info(f"\n🔍 You can explore the graph in Neo4j Browser with queries like:")
            logger.info(f"   MATCH (p:Person) RETURN p.name")
            logger.info(f"   MATCH (p:Person)-[r]->(target) RETURN p.name, type(r), target.name")
            logger.info(f"   MATCH (n:Industry) RETURN n.name")
            logger.info(f"   MATCH (n:Technology) RETURN n.name")
            
            return True
        else:
            logger.error("❌ Tests failed. Check the logs above for details.")
            return False
        
    except Exception as e:
        logger.error(f"❌ Test execution failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
